{"compilerOptions": {"target": "ES2018", "module": "ESNext", "moduleResolution": "Node", "lib": ["ESNext", "ESNext.AsyncIterable", "DOM"], "esModuleInterop": true, "allowJs": true, "sourceMap": true, "strict": false, "noEmit": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"~/*": ["./*"], "@/*": ["./*"]}, "types": ["node", "@nuxt/types", "vue-router", "vue-property-decorator", "nuxt-property-decorator"], "skipLibCheck": true, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "noUnusedLocals": false, "noUnusedParameters": false, "typeRoots": ["./types", "./node_modules/@types"]}, "exclude": ["node_modules", ".nuxt", "dist"], "include": ["**/*.ts", "**/*.tsx", "**/*.vue", "types/*.d.ts"]}