/* 全局变量 */
:root {
  /* 颜色 */
  /* 法律合规系统主题变量 */
  --lawyer-primary: #cf9b39;
  --lawyer-primary-light: #fbbf24;
  --lawyer-primary-dark: #d97706;
  --lawyer-primary-rgb: 245, 158, 11;
  --lawyer-success: #10b981;
  --lawyer-warning: #f59e0b;
  --lawyer-danger: #ef4444;
  --lawyer-danger-rgb: 239, 68, 68;
  --lawyer-upload: #4ecdc4;
  --lawyer-background: #f3f3f3;
  --lawyer-surface: #fff;
  --lawyer-text: #1e293b;
  --lawyer-text-light: #64748b;
  --lawyer-border: #e2e8f0;
  --lawyer-border-light: #f1f5f9;
  /* 文档差异对比颜色 */
  --lawyer-diff-add: #e6ffed;
  --lawyer-diff-add-border: #b7ebc0;
  --lawyer-diff-del: #ffeef0;
  --lawyer-diff-del-border: #fdb8c0;
}
/* 全局状态样式 */
.lawyer-status-approved {
  color: var(--lawyer-success);
}
.lawyer-status-rejected {
  color: var(--lawyer-danger);
}
.lawyer-status-pending {
  color: var(--lawyer-warning);
}
.ant-table-tbody > tr > td {
  padding: 12px 16px;
  vertical-align: middle;
}
/* 链接样式 */
.lawyer-link {
  color: var(--lawyer-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}
.lawyer-link:hover {
  color: var(--lawyer-primary-dark);
}
/* 按钮基本样式 */
.lawyer-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--lawyer-border);
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.15s ease-in-out;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  background: var(--lawyer-surface);
  color: var(--lawyer-text-light);
  font-family: inherit;
  outline: none;
}
.lawyer-btn:hover {
  border-color: var(--lawyer-primary);
  color: var(--lawyer-primary);
}
.lawyer-btn:focus {
  box-shadow: 0 0 0 2px rgba(var(--lawyer-primary-rgb), 0.2);
}
.lawyer-btn-primary {
  background: var(--lawyer-primary);
  color: white;
  border-color: var(--lawyer-primary);
}
.lawyer-btn-primary:hover {
  background: var(--lawyer-primary-dark);
  border-color: var(--lawyer-primary-dark);
  color: white;
}
.lawyer-btn-upload {
  background: var(--lawyer-upload);
  color: white;
  border-color: var(--lawyer-upload);
}
.lawyer-btn-upload:hover {
  background: #45b7aa;
  border-color: #45b7aa;
  color: white;
}
/* 页面容器 */
.lawyer-page-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 1.5rem;
}
.lawyer-page-container .ant-select,
.lawyer-page-container .ant-calendar-picker,
.lawyer-page-container .ant-input-number {
  width: inherit !important;
}
.lawyer-page-header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}
.lawyer-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(calc(50% - 0.75rem), 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}
.lawyer-stats-card {
  background: var(--lawyer-surface);
  border-radius: 8px;
  overflow: hidden;
}
.lawyer-stats-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
}
.lawyer-stats-card-header h2 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--lawyer-text);
  margin: 0;
}
.lawyer-more-action {
  border: none;
  background: transparent;
  padding: 0.25rem;
  border-radius: 4px;
  cursor: pointer;
}
.lawyer-chart-container {
  height: 250px;
  margin-bottom: 1rem;
}
.lawyer-chart-placeholder {
  width: 100%;
  height: 100%;
  background: var(--lawyer-border-light);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.lawyer-placeholder-text {
  font-size: 1rem;
  color: var(--lawyer-text-light);
}
.lawyer-stats-legend {
  display: flex;
  justify-content: space-around;
  margin-top: 1rem;
}
.lawyer-legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.lawyer-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
}
.lawyer-legend-label {
  font-size: 0.875rem;
  color: var(--lawyer-text-light);
}
.lawyer-legend-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--lawyer-text);
}
.lawyer-data-table {
  margin-bottom: 2rem;
}
.lawyer-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}
.lawyer-table-header h2 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--lawyer-text);
  margin: 0;
}
.lawyer-table-actions {
  display: flex;
  gap: 0.75rem;
}
/* 布局相关样式 */
.lawyer-app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.lawyer-main-content {
  background: var(--lawyer-background);
  flex: 1;
}
.lawyer-top-header {
  background: var(--lawyer-surface, #fff);
  padding: 0.75rem 2rem;
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid var(--lawyer-border, #e2e8f0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.lawyer-top-header-content {
  max-width: 1600px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.lawyer-top-logo {
  font-size: 1.375rem;
  font-weight: 600;
  color: var(--lawyer-primary, #f59e0b);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.lawyer-top-logo .anticon {
  font-size: 1.25rem;
}
.lawyer-nav-links {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}
.lawyer-nav-link {
  color: var(--lawyer-text-light, #64748b);
  text-decoration: none;
  transition: color 0.2s ease, border-color 0.2s ease;
  padding: 0.6rem 0.25rem;
  border-bottom: 2px solid transparent;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.4rem;
}
.lawyer-nav-link .anticon {
  font-size: 1rem;
}
.lawyer-nav-link:hover {
  color: var(--lawyer-primary-dark, #d97706);
  background-color: rgba(var(--lawyer-primary-rgb, 245, 158, 11), 0.05);
}
.lawyer-nav-link.active {
  color: var(--lawyer-primary, #f59e0b);
  border-bottom: 2px solid var(--lawyer-primary, #f59e0b);
  font-weight: 600;
}
.lawyer-nav-link.active:hover {
  background-color: transparent;
}
.lawyer-nav-link .lawyer-badge {
  background: var(--lawyer-danger, #ef4444);
  color: white;
  font-size: 0.65rem;
  padding: 0.1rem 0.4rem;
  border-radius: 0.5rem;
  font-weight: 600;
  margin-left: 0.3rem;
  line-height: 1;
}
.lawyer-pagination {
  display: flex;
  justify-content: center;
  padding: 32px 0;
}
/* 摘要弹窗样式 */
.lawyer-summary-content h2 {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: var(--lawyer-text, #1e293b);
}
.lawyer-summary-meta {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: var(--lawyer-text-light, #64748b);
}
.lawyer-summary-meta span {
  font-weight: 500;
  color: var(--lawyer-text, #1e293b);
}
.lawyer-summary-body {
  margin: 1rem 0;
  line-height: 1.8;
  color: var(--lawyer-text, #1e293b);
}
.lawyer-summary-footer {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}
.lawyer-flex {
  display: flex;
}
.lawyer-flex-col {
  display: flex;
  flex-direction: column;
}
.lawyer-items-center {
  align-items: center;
}
.lawyer-justify-between {
  justify-content: space-between;
}
.lawyer-gap-sm {
  gap: 8px;
}
.lawyer-gap-md {
  gap: 15px;
}
.lawyer-gap-lg {
  gap: 20px;
}
.lawyer-card {
  background: var(--lawyer-surface);
  border: 1px solid var(--lawyer-border);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}
.lawyer-scrollable {
  overflow-y: auto;
}
.lawyer-scrollable::-webkit-scrollbar {
  width: 6px;
}
.lawyer-scrollable::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}
.lawyer-text-primary {
  color: var(--lawyer-primary);
}
.lawyer-text-light {
  color: var(--lawyer-text-light);
}
/* Markdown 内容公共样式 */
.lawyer-markdown-content .github-markdown-body {
  padding: 0;
  font-size: inherit;
}
.lawyer-markdown-content h1,
.lawyer-markdown-content h2,
.lawyer-markdown-content h3,
.lawyer-markdown-content h4,
.lawyer-markdown-content h5,
.lawyer-markdown-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
  color: var(--lawyer-primary);
  border: none;
}
.lawyer-markdown-content h1 {
  font-size: 1.5em;
  border-bottom: 2px solid var(--lawyer-primary);
  padding-bottom: 8px;
}
.lawyer-markdown-content h2 {
  font-size: 1.3em;
  border-bottom: 1px solid #e1e4e8;
  padding-bottom: 6px;
}
.lawyer-markdown-content h3 {
  font-size: 1.1em;
  color: var(--lawyer-primary);
  font-weight: 600;
}
.lawyer-markdown-content h4 {
  font-size: 1.05em;
  color: #586069;
}
.lawyer-markdown-content h5 {
  font-size: 1em;
  color: #586069;
}
.lawyer-markdown-content h6 {
  font-size: 0.95em;
  color: #6a737d;
}
.lawyer-markdown-content p {
  margin: 8px 0;
  line-height: 1.6;
  color: #24292e;
}
.lawyer-markdown-content ul,
.lawyer-markdown-content ol {
  margin: 8px 0;
  padding-left: 20px;
}
.lawyer-markdown-content li {
  margin: 4px 0;
  line-height: 1.5;
}
.lawyer-markdown-content code {
  background: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "SFMono-Regular", "Consolas", "Liberation Mono", "Menlo", monospace;
  font-size: 0.85em;
  color: #e36209;
}
.lawyer-markdown-content pre {
  background: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
  border: 1px solid #e1e4e8;
}
.lawyer-markdown-content pre code {
  background: none;
  padding: 0;
  color: #24292e;
  font-size: 0.85em;
}
.lawyer-markdown-content blockquote {
  border-left: 4px solid #dfe2e5;
  margin: 16px 0;
  padding: 0 16px;
  background: #f6f8fa;
  color: #6a737d;
  border-radius: 3px;
}
.lawyer-markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  overflow: hidden;
}
.lawyer-markdown-content th,
.lawyer-markdown-content td {
  border: 1px solid #e1e4e8;
  padding: 6px 13px;
  text-align: left;
}
.lawyer-markdown-content th {
  background: #f6f8fa;
  font-weight: 600;
  color: #24292e;
}
.lawyer-markdown-content td {
  color: #24292e;
}
.lawyer-markdown-content strong {
  font-weight: 600;
  color: var(--lawyer-primary);
}
.lawyer-markdown-content em {
  font-style: italic;
  color: #6f42c1;
}
.lawyer-markdown-content hr {
  height: 0.25em;
  padding: 0;
  margin: 24px 0;
  background-color: #e1e4e8;
  border: 0;
}
.lawyer-markdown-content a {
  color: #0366d6;
  text-decoration: none;
}
.lawyer-markdown-content a:hover {
  text-decoration: underline;
}
.lawyer-markdown-content del {
  text-decoration: line-through;
  color: #6a737d;
}
.lawyer-markdown-content * {
  word-wrap: break-word;
  word-break: break-word;
}
