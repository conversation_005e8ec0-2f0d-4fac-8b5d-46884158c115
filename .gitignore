# 依赖目录
node_modules/
pnpm-store/
.yarn/
原形页面/
# 构建输出
dist/
build/
*.zip

# 本地环境变量
.env.local
.env.*.local

# 日志文件
*.log
logs/

# 编辑器/IDE 配置
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 系统文件
.DS_Store
Thumbs.db

# 测试相关
coverage/
.nyc_output/
jest.config.js
cypress/screenshots/
cypress/videos/

# Vue CLI 缓存
.vue/cli-service/
.vue/cli-plugin-*/

# Vite 缓存
.cache/
.temp/

# TypeScript 缓存
*.tsbuildinfo

# ESLint/Prettier 缓存
.eslintcache
.prettiercache

# 包管理器文件
package-lock.json
yarn.lock
pnpm-lock.yaml
shrinkwrap.yaml

# 自定义配置文件
*.config.js
*.conf.js